import QRichTable from '@/components/global/q-rich-table';
import type { IQRichTableColumn } from '@/components/global/q-rich-table';
import { defineComponent, VNode, ref, computed, PropType, onUnmounted, onMounted } from 'vue';
import VueDraggableResizable from 'vue-draggable-resizable';
import 'vue-draggable-resizable/dist/VueDraggableResizable.css';

/**
 * 拖拽状态映射接口
 */
export interface DraggingState {
  [key: string]: number;
}

export default defineComponent({
  name: 'QDraggableTable',
  props: {
    columns: {
      type: Array as PropType<IQRichTableColumn[]>,
      default: () => [],
      required: true,
    },
  },
  setup(props) {
    // 容器宽度
    const containerWidth = ref<number>(1200);

    // 计算后的实际列配置
    const computedColumns = ref<IQRichTableColumn[]>([]);

    /**
     * 计算列宽分配
     * 固定宽度列保持不变，可变宽度列按比例分配剩余空间
     */
    const calculateColumnWidths = () => {
      const fixedColumns = props.columns.filter((col) => col.width);
      const flexColumns = props.columns.filter((col) => col.minWidth && !col.width);

      // 计算固定列总宽度
      const fixedTotalWidth = fixedColumns.reduce((sum, col) => sum + (Number(col.width) || 0), 0);

      // 计算可变列最小宽度总和
      const minFlexTotalWidth = flexColumns.reduce((sum, col: IQRichTableColumn) => sum + (col.minWidth || 0), 0);

      // 剩余可分配宽度
      const remainingWidth = containerWidth.value - fixedTotalWidth;

      console.log('remainingWidth', remainingWidth);
      // 如果剩余宽度不足以满足最小宽度要求，使用最小宽度
      if (remainingWidth < minFlexTotalWidth) {
        computedColumns.value = props.columns.map((col) => ({
          ...col,
          width: col.width || col.minWidth,
        }));
        return;
      }

      // 确保总宽度不超过容器宽度
      // 如果剩余宽度正好等于最小宽度总和，直接使用最小宽度
      if (remainingWidth === minFlexTotalWidth) {
        computedColumns.value = props.columns.map((col) => ({
          ...col,
          width: col.width || col.minWidth,
        }));
        return;
      }

      // 当剩余宽度大于最小宽度总和时，平均分配剩余宽度
      const extraWidth = remainingWidth - minFlexTotalWidth;
      const extraWidthPerColumn = Math.floor(extraWidth / flexColumns.length);
      let remainingExtraWidth = extraWidth - extraWidthPerColumn * flexColumns.length;

      computedColumns.value = props.columns.map((col, index): IQRichTableColumn[] => {
        if (col.width) {
          // 固定宽度列保持不变
          return { ...col };
        } else if (col.minWidth) {
          // 可变宽度列 = 最小宽度 + 平均分配的额外宽度
          let finalWidth = col.minWidth + extraWidthPerColumn;

          // 将剩余的像素分配给前几列
          if (remainingExtraWidth > 0) {
            finalWidth += 1;
            remainingExtraWidth -= 1;
          }

          return {
            ...col,
            width: finalWidth,
          };
        }
        return col;
      });
    };

    /**
     * 动态计算表格横向滚动宽度
     * 只有当表格总宽度超过容器宽度时才设置scrollX
     */
    const scrollX = computed(() => {
      const totalWidth = computedColumns.value.reduce((preVal, curVal) => {
        return preVal + (Number(curVal.width) || 0);
      }, 0);

      // 如果表格总宽度不超过容器宽度，不设置scrollX，避免出现滚动条区域
      return totalWidth > containerWidth.value ? totalWidth : undefined;
    });

    /**
     * 监听容器宽度变化
     */
    const updateContainerWidth = () => {
      const tableContainer = document.querySelector('.draggable-table-container');
      if (tableContainer) {
        containerWidth.value = tableContainer.clientWidth - 33;
        calculateColumnWidths();
      }
    };

    // 防抖函数
    let resizeTimer: number | null = null;
    const debouncedResize = () => {
      if (resizeTimer) {
        clearTimeout(resizeTimer);
      }
      resizeTimer = window.setTimeout(updateContainerWidth, 100);
    };
    const draggingState = ref<DraggingState>({});
    /**
     * 初始化拖拽功能
     * 为表格列添加拖拽调整宽度的功能
     * @param cols - 表格列配置数组
     */
    const initDrag = (cols: IQRichTableColumn[]) => {
      const draggingMap: DraggingState = {};
      cols.forEach((col) => {
        if (col.key) {
          draggingMap[`${col.key}`] = Number(col.width || col.minWidth || 0);
        } else {
          throw new Error('列配置必须有key属性');
        }
      });
      draggingState.value = draggingMap;
    };

    /**
     * 可调整大小的表头渲染函数
     * @param props - 表头属性
     * @param children - 子元素
     */
    const componentsHeaderCell = (h, props: any, children: any) => {
      let thDom: HTMLElement | null = null;
      const { key, ...restProps } = props;
      const col = computedColumns.value.find((col) => {
        const k = col.dataIndex || col.key;
        return k === key;
      });

      // 限制操作列和固定宽度列不可伸缩
      if (col?.width && !col?.minWidth) {
        return h('th', restProps, children);
      }

      // 如果既没有width也没有minWidth，不可拖拽
      if (!col?.width && !col?.minWidth) {
        return h('th', restProps, children);
      }

      /**
       * 拖拽过程中的处理函数
       * @param x - 拖拽的x坐标
       */
      const onDrag = (x: number) => {
        draggingState.value[key] = 0;
        const minWidth = col.minWidth || 100;
        let width = Math.max(x, minWidth); // 使用配置的最小宽度
        width = Math.min(width, 500); // 最大宽度500px

        // 如果是可变宽度列，更新minWidth而不是width
        if (col.minWidth && !col.width) {
          col.minWidth = width;
          // 重新计算所有列宽
          calculateColumnWidths();
        } else {
          col.width = width;
        }
      };

      /**
       * 拖拽结束时的处理函数
       */
      const onDragstop = () => {
        if (thDom) {
          draggingState.value[key] = thDom.getBoundingClientRect().width;
        }
      };

      return (
        <th {...restProps} v-ant-ref={(r) => (thDom = r)} width={draggingState[key]}>
          {children}
          <VueDraggableResizable
            key={col.key}
            w={10}
            x={col.width || draggingState[key]}
            z={1}
            axis="x"
            draggable={true}
            resizable={false}
            onDragging={onDrag}
            onDragstop={onDragstop}
          ></VueDraggableResizable>
        </th>
      );
    };
    // 组件挂载时初始化
    onMounted(() => {
      // 初始化列宽计算
      calculateColumnWidths();
      // 初始化拖拽功能
      // 使用类型断言确保computedColumns.value的类型正确
      initDrag(computedColumns.value as IQRichTableColumn[]);
      // 添加窗口大小监听
      window.addEventListener('resize', debouncedResize);
      // 初始化容器宽度
      setTimeout(updateContainerWidth, 100);
    });

    // 组件卸载时清理监听器
    onUnmounted(() => {
      window.removeEventListener('resize', debouncedResize);
      if (resizeTimer) {
        clearTimeout(resizeTimer);
      }
    });
    return {
      componentsHeaderCell,
      scrollX,
      computedColumns,
    };
  },
  render() {
    const data = {
      attrs: this.$attrs,
      props: {},
      on: {},
    };
    console.log('computedColumns', this.computedColumns);
    return (
      <QRichTable
        {...data}
        showIndex={false}
        componentsHeaderCell={this.componentsHeaderCell}
        columns={this.computedColumns}
        scroll={{ x: this.scrollX }}
      />
    );
  },
});
